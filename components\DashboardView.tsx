
import React from 'react';
import { useAppData } from '../hooks/useAppData';
import { UsersIcon, CheckIcon, WarningIcon, ChartBarIcon } from './icons'; // UserIcon removed as not used
import { getMonthName } from '../utils/dateUtils';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  colorClass: string;
  description?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, colorClass, description }) => (
  <div className={`bg-white p-5 shadow-lg rounded-xl border-l-4 ${colorClass}`}>
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-500 uppercase tracking-wider">{title}</p>
        <p className="text-3xl font-semibold text-gray-800">{value}</p>
        {description && <p className="text-xs text-gray-400 mt-1">{description}</p>}
      </div>
      <div className="text-gray-300">{icon}</div>
    </div>
  </div>
);


const DashboardView: React.FC = () => {
  const { members, attendanceRecords, criticalMemberIds, bacentas, displayedSundays, displayedDate } = useAppData(); // Use displayedSundays

  const totalMembers = members.length;
  
  const currentMonthAttendancePercentage = () => {
    if (!displayedSundays.length || !members.length) return 0;
    
    let totalPossibleAttendances = members.length * displayedSundays.length;
    let actualPresents = 0;

    displayedSundays.forEach(sunday => {
      members.forEach(member => {
        const record = attendanceRecords.find(ar => ar.memberId === member.id && ar.date === sunday);
        if (record && record.status === 'Present') {
          actualPresents++;
        }
      });
    });
    
    return totalPossibleAttendances > 0 ? Math.round((actualPresents / totalPossibleAttendances) * 100) : 0;
  };

  const membersPerBacenta = bacentas.map(b => { // Renamed from membersPerCongregation
    const count = members.filter(m => m.bacentaId === b.id).length; // Use bacentaId
    return { name: b.name, count };
  });

  const monthName = getMonthName(displayedDate.getMonth());
  const year = displayedDate.getFullYear();

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-gray-700 mb-6">Dashboard - {monthName} {year}</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard 
          title="Total Members" 
          value={totalMembers} 
          icon={<UsersIcon className="w-12 h-12" />}
          colorClass="border-blue-500"
        />
        <StatCard 
          title="Attendance Rate" 
          value={`${currentMonthAttendancePercentage()}%`}
          icon={<CheckIcon className="w-12 h-12" />}
          colorClass="border-green-500"
          description={`For ${monthName}`}
        />
        <StatCard 
          title="Critical Alerts" 
          value={criticalMemberIds.length}
          icon={<WarningIcon className="w-12 h-12" />}
          colorClass="border-red-500"
          description="Members needing follow-up"
        />
      </div>

      <div className="bg-white p-6 shadow-lg rounded-xl">
        <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
            <ChartBarIcon className="w-6 h-6 mr-2 text-indigo-500" />
            Members per Bacenta {/* Renamed title */}
        </h3>
        {membersPerBacenta.length > 0 ? (
          <ul className="space-y-3">
            {membersPerBacenta.map(item => (
              <li key={item.name} className="flex justify-between items-center p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors">
                <span className="text-gray-600">{item.name}</span>
                <span className="font-semibold text-indigo-600 bg-indigo-100 px-2 py-1 rounded-full text-sm">{item.count}</span>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-500 italic">No Bacentas created or no members assigned.</p> // Updated message
        )}
      </div>
    </div>
  );
};

export default DashboardView;
