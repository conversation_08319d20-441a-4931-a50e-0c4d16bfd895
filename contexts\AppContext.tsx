
import React, { createContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { Member, AttendanceRecord, Bacenta, TabOption, AttendanceStatus, TabKeys } from '../types'; // Bacenta instead of CongregationGroup
import { MemberService, AttendanceService, BacentaService, initializeDataIfNeeded } from '../services/dataService'; // BacentaService
import { FIXED_TABS, CONSECUTIVE_ABSENCE_THRESHOLD, DEFAULT_TAB_ID } from '../constants'; // CONGREGATION_GROUPS removed
import { getSundaysOfMonth, formatDateToYYYYMMDD } from '../utils/dateUtils';

interface AppContextType {
  members: Member[];
  attendanceRecords: AttendanceRecord[];
  bacentas: Bacenta[]; // Renamed from congregations
  currentTab: TabOption;
  isLoading: boolean;
  error: string | null;
  displayedSundays: string[]; // Renamed from sundaysThisMonth
  displayedDate: Date; // For month navigation
  criticalMemberIds: string[];
  isMemberFormOpen: boolean;
  editingMember: Member | null;
  isBacentaFormOpen: boolean; // New state for Bacenta form
  editingBacenta: Bacenta | null; // New state for Bacenta form
  fetchInitialData: () => Promise<void>;
  addMemberHandler: (memberData: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>) => Promise<void>;
  updateMemberHandler: (memberData: Member) => Promise<void>;
  deleteMemberHandler: (memberId: string) => Promise<void>;
  markAttendanceHandler: (memberId: string, date: string, status: AttendanceStatus) => Promise<void>;
  changeTab: (tabId: string) => void;
  openMemberForm: (member: Member | null) => void;
  closeMemberForm: () => void;
  refreshData: () => Promise<void>;
  addBacentaHandler: (name: string) => Promise<Bacenta | null>; // New handler
  updateBacentaHandler: (bacenta: Bacenta) => Promise<void>; // New handler
  deleteBacentaHandler: (bacentaId: string) => Promise<void>; // New handler
  openBacentaForm: (bacenta: Bacenta | null) => void; // New function
  closeBacentaForm: () => void; // New function
  navigateToPreviousMonth: () => void; // For month navigation
  navigateToNextMonth: () => void; // For month navigation
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [members, setMembers] = useState<Member[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [bacentas, setBacentas] = useState<Bacenta[]>([]);
  const [currentTab, setCurrentTab] = useState<TabOption>(FIXED_TABS.find(t => t.id === DEFAULT_TAB_ID) || FIXED_TABS[0]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const [displayedDate, setDisplayedDate] = useState<Date>(new Date()); // Initialize with current date
  const [displayedSundays, setDisplayedSundays] = useState<string[]>([]); // Renamed
  const [criticalMemberIds, setCriticalMemberIds] = useState<string[]>([]);
  
  const [isMemberFormOpen, setIsMemberFormOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<Member | null>(null);

  const [isBacentaFormOpen, setIsBacentaFormOpen] = useState(false);
  const [editingBacenta, setEditingBacenta] = useState<Bacenta | null>(null);


  const calculateCriticalMembers = useCallback((mems: Member[], attRecs: AttendanceRecord[], sundays: string[]) => {
    if (!sundays.length) return [];
    
    const criticalIds: string[] = [];
    const sortedSundays = [...sundays].sort((a,b) => new Date(a).getTime() - new Date(b).getTime());

    mems.forEach(member => {
      let consecutiveAbsences = 0;
      let maxConsecutiveAbsences = 0;
      
      // Check from the most recent Sunday in the displayedSundays list
      for (const sundayDate of sortedSundays.slice().reverse()) {
        const record = attRecs.find(ar => ar.memberId === member.id && ar.date === sundayDate);
        if (record && record.status === 'Absent') {
          consecutiveAbsences++;
        } else if (record && record.status === 'Present') {
          break; // Streak broken by presence
        } else {
           // No record for this Sunday, can't determine absence for sure for the streak from this point
           // For simplicity, we'll consider no record as breaking the *consecutive* absence streak for *this specific calculation*
           // A more robust system might require records for all Sundays or handle this differently.
           break; 
        }
        if (consecutiveAbsences >= CONSECUTIVE_ABSENCE_THRESHOLD) {
          maxConsecutiveAbsences = consecutiveAbsences;
          break;
        }
      }

      if (maxConsecutiveAbsences >= CONSECUTIVE_ABSENCE_THRESHOLD) {
        criticalIds.push(member.id);
      }
    });
    return criticalIds;
  }, []);

  const loadData = useCallback(async (targetDate: Date = displayedDate) => {
    setIsLoading(true);
    setError(null);
    try {
      await initializeDataIfNeeded();
      const [fetchedMembers, fetchedAttendance, fetchedBacentas] = await Promise.all([
        MemberService.getMembers(),
        AttendanceService.getAttendance(),
        BacentaService.getBacentas(),
      ]);
      setMembers(fetchedMembers);
      setAttendanceRecords(fetchedAttendance);
      setBacentas(fetchedBacentas);

      const sundays = getSundaysOfMonth(targetDate.getFullYear(), targetDate.getMonth());
      setDisplayedSundays(sundays);
      
      const criticalIds = calculateCriticalMembers(fetchedMembers, fetchedAttendance, sundays);
      setCriticalMemberIds(criticalIds);

    } catch (e) {
      console.error("Failed to load data:", e);
      setError("Failed to load data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [calculateCriticalMembers, displayedDate]); // Add displayedDate to dependency if it's used to determine targetDate

  const fetchInitialData = () => loadData(new Date()); // Load current month initially
  const refreshData = () => loadData(displayedDate); // Refresh based on currently displayed date


  useEffect(() => {
    // This effect recalculates sundays and critical members when displayedDate changes
    const sundays = getSundaysOfMonth(displayedDate.getFullYear(), displayedDate.getMonth());
    setDisplayedSundays(sundays);
    const criticalIds = calculateCriticalMembers(members, attendanceRecords, sundays);
    setCriticalMemberIds(criticalIds);
  }, [displayedDate, members, attendanceRecords, calculateCriticalMembers]);


  const addMemberHandler = async (memberData: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>) => {
    setIsLoading(true);
    try {
      const newMember = await MemberService.addMember(memberData);
      setMembers(prev => [...prev, newMember]);
    } catch (e) {
      setError("Failed to add member.");
    } finally {
      setIsLoading(false);
    }
  };

  const updateMemberHandler = async (memberData: Member) => {
    setIsLoading(true);
    try {
      const updatedMember = await MemberService.updateMember(memberData);
      setMembers(prev => prev.map(m => m.id === updatedMember.id ? updatedMember : m));
    } catch (e) {
      setError("Failed to update member.");
    } finally {
      setIsLoading(false);
    }
  };

  const deleteMemberHandler = async (memberId: string) => {
    if (!window.confirm("Are you sure you want to delete this member and all their attendance records?")) {
      return;
    }
    setIsLoading(true);
    try {
      await MemberService.deleteMember(memberId);
      setMembers(prev => prev.filter(m => m.id !== memberId));
      setAttendanceRecords(prev => prev.filter(ar => ar.memberId !== memberId));
    } catch (e) {
      setError("Failed to delete member.");
    } finally {
      setIsLoading(false);
    }
  };

  const markAttendanceHandler = async (memberId: string, date: string, status: AttendanceStatus) => {
    try {
      const updatedRecord = await AttendanceService.markAttendance(memberId, date, status);
      setAttendanceRecords(prev => {
        const existingIndex = prev.findIndex(ar => ar.id === updatedRecord.id);
        if (existingIndex > -1) {
          const newRecords = [...prev];
          newRecords[existingIndex] = updatedRecord;
          return newRecords;
        }
        return [...prev, updatedRecord];
      });
      // Critical members will be recalculated by the useEffect due to attendanceRecords change
    } catch (e) {
      setError("Failed to mark attendance.");
    }
  };

  const changeTab = (tabId: string) => {
    const allTabs = [...FIXED_TABS, ...bacentas.map(b => ({ id: b.id, name: b.name }))];
    const newTab = allTabs.find(t => t.id === tabId) || FIXED_TABS[0];
    setCurrentTab(newTab);
  };
  
  const openMemberForm = (member: Member | null) => {
    setEditingMember(member);
    setIsMemberFormOpen(true);
  };

  const closeMemberForm = () => {
    setEditingMember(null);
    setIsMemberFormOpen(false);
  };

  // Bacenta Handlers
  const addBacentaHandler = async (name: string): Promise<Bacenta | null> => {
    setIsLoading(true);
    try {
      const newBacenta = await BacentaService.addBacenta(name);
      setBacentas(prev => [...prev, newBacenta]);
      return newBacenta;
    } catch (e) {
      setError("Failed to add Bacenta.");
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const updateBacentaHandler = async (bacentaData: Bacenta) => {
    setIsLoading(true);
    try {
      const updatedBacenta = await BacentaService.updateBacenta(bacentaData);
      setBacentas(prev => prev.map(b => b.id === updatedBacenta.id ? updatedBacenta : b));
    } catch (e) {
      setError("Failed to update Bacenta.");
    } finally {
      setIsLoading(false);
    }
  };

  const deleteBacentaHandler = async (bacentaId: string) => {
    if (!window.confirm("Are you sure you want to delete this Bacenta? Members assigned to it will be unassigned.")) {
      return;
    }
    setIsLoading(true);
    try {
      await BacentaService.deleteBacenta(bacentaId);
      setBacentas(prev => prev.filter(b => b.id !== bacentaId));
      setMembers(prevMembers => prevMembers.map(m => 
        m.bacentaId === bacentaId ? { ...m, bacentaId: '', lastUpdated: new Date().toISOString() } : m
      ));
      if (currentTab.id === bacentaId) {
        changeTab(TabKeys.ALL_CONGREGATIONS);
      }
    } catch (e) {
      setError("Failed to delete Bacenta.");
    } finally {
      setIsLoading(false);
    }
  };

  const openBacentaForm = (bacenta: Bacenta | null) => {
    setEditingBacenta(bacenta);
    setIsBacentaFormOpen(true);
  };

  const closeBacentaForm = () => {
    setEditingBacenta(null);
    setIsBacentaFormOpen(false);
  };

  // Month Navigation Handlers
  const navigateToPreviousMonth = () => {
    setDisplayedDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() - 1);
      return newDate;
    });
  };

  const navigateToNextMonth = () => {
    setDisplayedDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() + 1);
      return newDate;
    });
  };

  return (
    <AppContext.Provider value={{
      members, attendanceRecords, bacentas, currentTab, isLoading, error, 
      displayedSundays, displayedDate, criticalMemberIds,
      isMemberFormOpen, editingMember, isBacentaFormOpen, editingBacenta,
      fetchInitialData, addMemberHandler, updateMemberHandler, deleteMemberHandler, markAttendanceHandler, changeTab,
      openMemberForm, closeMemberForm, refreshData,
      addBacentaHandler, updateBacentaHandler, deleteBacentaHandler, openBacentaForm, closeBacentaForm,
      navigateToPreviousMonth, navigateToNextMonth
    }}>
      {children}
    </AppContext.Provider>
  );
};

export default AppContext;
