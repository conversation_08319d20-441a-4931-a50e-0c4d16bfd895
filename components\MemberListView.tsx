
import React, { useState, useMemo } from 'react';
import { useAppData } from '../hooks/useAppData';
import MemberCard from './MemberCard';
import { LoadingSpinnerIcon, SearchIcon, UsersIcon, CalendarIcon, ChevronLeftIcon, ChevronRightIcon } from './icons'; // Added Calendar, Chevron icons
import Input from './ui/Input';
import Button from './ui/Button';
import { getMonthName } from '../utils/dateUtils';


interface MemberListViewProps {
  bacentaFilter: string | null; 
}

const MemberListView: React.FC<MemberListViewProps> = ({ bacentaFilter }) => {
  const { 
    members, 
    isLoading, 
    criticalMemberIds, 
    bacentas, 
    currentTab,
    displayedDate, // For month navigation
    navigateToPreviousMonth, // For month navigation
    navigateToNextMonth, // For month navigation
  } = useAppData();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredMembers = useMemo(() => {
    return members
      .filter(member => {
        if (bacentaFilter && member.bacentaId !== bacentaFilter) {
          return false;
        }
        if (searchTerm) {
          const lowerSearchTerm = searchTerm.toLowerCase();
          return (
            member.firstName.toLowerCase().includes(lowerSearchTerm) ||
            member.lastName.toLowerCase().includes(lowerSearchTerm) ||
            member.phoneNumber.toLowerCase().includes(lowerSearchTerm) ||
            (member.bacentaId && bacentas.find(b => b.id === member.bacentaId)?.name.toLowerCase().includes(lowerSearchTerm)) || // Search by Bacenta name
            member.buildingAddress.toLowerCase().includes(lowerSearchTerm)
          );
        }
        return true;
      })
      .sort((a, b) => a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName));
  }, [members, bacentaFilter, searchTerm, bacentas]);

  if (isLoading && !members.length) {
    return (
      <div className="flex flex-col items-center justify-center py-10">
        <LoadingSpinnerIcon className="w-10 h-10 text-blue-500" />
        <p className="mt-2 text-gray-500">Loading members...</p>
      </div>
    );
  }
  
  const currentMonthName = getMonthName(displayedDate.getMonth());
  const currentYear = displayedDate.getFullYear();

  return (
    <div>
      <div className="mb-6 p-4 bg-white shadow rounded-lg">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-700 flex items-center">
              <UsersIcon className="w-6 h-6 mr-2 text-blue-500" /> 
              {currentTab.name}
            </h2>
            <p className="text-sm text-gray-500">{filteredMembers.length} member(s) found</p>
          </div>
          <div className="mt-3 sm:mt-0 w-full sm:w-auto sm:max-w-xs">
            <Input 
              type="text"
              placeholder="Search members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
              wrapperClassName="relative mb-0"
              aria-label="Search members"
            />
            <SearchIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
          </div>
        </div>
        
        {/* Attendance Month Navigation */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-medium text-gray-700 flex items-center">
              <CalendarIcon className="w-5 h-5 mr-2 text-indigo-500" />
              Attendance for: {currentMonthName} {currentYear}
            </h3>
            <div className="flex space-x-2">
              <Button onClick={navigateToPreviousMonth} size="sm" variant="ghost" aria-label="Previous month for attendance">
                <ChevronLeftIcon className="w-5 h-5" />
                <span className="hidden sm:inline ml-1">Prev</span>
              </Button>
              <Button onClick={navigateToNextMonth} size="sm" variant="ghost" aria-label="Next month for attendance">
                <span className="hidden sm:inline mr-1">Next</span>
                <ChevronRightIcon className="w-5 h-5" />
              </Button>
            </div>
          </div>
           <p className="text-xs text-gray-500 mb-3">Attendance markers below reflect records for {currentMonthName} {currentYear}.</p>
        </div>
      </div>


      {filteredMembers.length === 0 && !isLoading && (
        <div className="text-center py-10">
          <UsersIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">No members found.</p>
          {searchTerm && <p className="text-gray-400 text-sm">Try adjusting your search or filter.</p>}
          {!bacentaFilter && !searchTerm && <p className="text-gray-400 text-sm">Add members using the green '+' button.</p>}
          {bacentaFilter && !searchTerm && <p className="text-gray-400 text-sm">No members in this Bacenta yet, or matching current search/filters.</p>}
        </div>
      )}

      <div className="space-y-4">
        {filteredMembers.map(member => (
          <MemberCard 
            key={member.id} 
            member={member} 
            isCritical={criticalMemberIds.includes(member.id)} 
          />
        ))}
      </div>
    </div>
  );
};

export default MemberListView;
