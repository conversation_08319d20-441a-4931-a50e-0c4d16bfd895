
import React from 'react';
import { useAppData } from '../hooks/useAppData';
import MemberCard from './MemberCard';
import { WarningIcon, LoadingSpinnerIcon, CalendarIcon, ChevronLeftIcon, ChevronRightIcon } from './icons';
import { CONSECUTIVE_ABSENCE_THRESHOLD } from '../constants';
import Button from './ui/Button';
import { getMonthName } from '../utils/dateUtils';

const CriticalMembersView: React.FC = () => {
  const { 
    members, 
    criticalMemberIds, 
    isLoading,
    displayedDate, // For month navigation
    navigateToPreviousMonth, // For month navigation
    navigateToNextMonth, // For month navigation
    displayedSundays // To check if any sundays for context
  } = useAppData();

  const criticalMembers = members.filter(member => criticalMemberIds.includes(member.id))
    .sort((a,b) => a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName));

  if (isLoading && !criticalMembers.length && !displayedSundays.length) { // Check displayedSundays to avoid flicker on month change
    return (
      <div className="flex flex-col items-center justify-center py-10">
        <LoadingSpinnerIcon className="w-10 h-10 text-red-500" />
        <p className="mt-2 text-gray-500">Loading critical member data...</p>
      </div>
    );
  }
  
  const currentMonthName = getMonthName(displayedDate.getMonth());
  const currentYear = displayedDate.getFullYear();

  return (
    <div>
      <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 shadow rounded-lg">
        <h2 className="text-xl font-semibold text-red-700 flex items-center">
          <WarningIcon className="w-6 h-6 mr-2" />
          Critical Member Alerts
        </h2>
        <p className="text-sm text-red-600">
          {criticalMembers.length > 0 
            ? `${criticalMembers.length} member(s) flagged with ${CONSECUTIVE_ABSENCE_THRESHOLD} or more consecutive absences in ${currentMonthName} ${currentYear}.`
            : `No members flagged as critical for ${currentMonthName} ${currentYear}.`}
        </p>
      </div>
      
      {/* Attendance Month Navigation */}
       <div className="mb-6 p-4 bg-white shadow rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-medium text-gray-700 flex items-center">
            <CalendarIcon className="w-5 h-5 mr-2 text-indigo-500" />
            Viewing Absences For: {currentMonthName} {currentYear}
          </h3>
          <div className="flex space-x-2">
            <Button onClick={navigateToPreviousMonth} size="sm" variant="ghost" aria-label="Previous month for attendance">
              <ChevronLeftIcon className="w-5 h-5" />
              <span className="hidden sm:inline ml-1">Prev</span>
            </Button>
            <Button onClick={navigateToNextMonth} size="sm" variant="ghost" aria-label="Next month for attendance">
              <span className="hidden sm:inline mr-1">Next</span>
              <ChevronRightIcon className="w-5 h-5" />
            </Button>
          </div>
        </div>
        <p className="text-xs text-gray-500">
          Critical status is based on attendance records for the month of {currentMonthName} {currentYear}.
        </p>
      </div>


      {criticalMembers.length === 0 && !isLoading && (
         <div className="text-center py-10">
          <WarningIcon className="w-16 h-16 text-green-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">No critical members for {currentMonthName} {currentYear}.</p>
          <p className="text-gray-400 text-sm">All members have good attendance recently or no absences meeting the threshold!</p>
        </div>
      )}

      <div className="space-y-4">
        {criticalMembers.map(member => (
          <MemberCard key={member.id} member={member} isCritical={true} />
        ))}
      </div>
    </div>
  );
};

export default CriticalMembersView;
