
import React, { useState, useEffect } from 'react';
import { Bacenta } from '../types';
import { useAppData } from '../hooks/useAppData';
import Modal from './ui/Modal';
import Input from './ui/Input';
import Button from './ui/Button';

interface BacentaFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  bacenta: Bacenta | null; // Current bacenta for editing, or null for new
}

const BacentaFormModal: React.FC<BacentaFormModalProps> = ({ isOpen, onClose, bacenta }) => {
  const { addBacentaHandler, updateBacentaHandler, changeTab } = useAppData();
  const [name, setName] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (bacenta) {
      setName(bacenta.name);
    } else {
      setName('');
    }
    setError(null); // Clear error when modal opens or bacenta changes
  }, [isOpen, bacenta]);

  const validate = (): boolean => {
    if (!name.trim()) {
      setError('Bacenta name cannot be empty.');
      return false;
    }
    setError(null);
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) return;

    if (bacenta) { // Editing existing Bacenta
      await updateBacentaHandler({ ...bacenta, name });
    } else { // Adding new Bacenta
      const newBacenta = await addBacentaHandler(name);
      if (newBacenta) {
        changeTab(newBacenta.id); // Switch to the new Bacenta tab
      }
    }
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={bacenta ? 'Edit Bacenta' : 'Add New Bacenta'} size="md">
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="Bacenta Name"
          name="bacentaName"
          value={name}
          onChange={(e) => setName(e.target.value)}
          error={error || undefined}
          required
          autoFocus
        />
        <div className="flex justify-end space-x-3 pt-4">
          <Button type="button" variant="secondary" onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="primary">{bacenta ? 'Save Changes' : 'Add Bacenta'}</Button>
        </div>
      </form>
    </Modal>
  );
};

export default BacentaFormModal;
