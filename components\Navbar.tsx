
import React from 'react';
import { useAppData } from '../hooks/useAppData';
import { FIXED_TABS } from '../constants';
import { TabOption } from '../types';
import { ChartBarIcon, UsersIcon, WarningIcon, GroupIcon, PlusCircleIcon, EditIcon, TrashIcon } from './icons'; // Added PlusCircleIcon, GroupIcon, EditIcon, TrashIcon

const Navbar: React.FC = () => {
  const { bacentas, currentTab, changeTab, criticalMemberIds, openBacentaForm, deleteBacentaHandler } = useAppData(); // Use bacentas

  const allTabs: TabOption[] = [
    ...FIXED_TABS,
    ...bacentas.map(b => ({ id: b.id, name: b.name })), // Use bacentas
  ];

  const getIconForTab = (tabId: string) => {
    switch (tabId) {
      case 'dashboard': return <ChartBarIcon className="w-5 h-5 mr-1 sm:mr-2" />;
      case 'all_members': return <UsersIcon className="w-5 h-5 mr-1 sm:mr-2" />;
      case 'critical_members': return <WarningIcon className="w-5 h-5 mr-1 sm:mr-2" />;
      default: return <GroupIcon className="w-5 h-5 mr-1 sm:mr-2" />; // Generic icon for Bacentas
    }
  };

  const isBacentaTab = (tabId: string) => FIXED_TABS.every(ft => ft.id !== tabId);

  return (
    <nav className="bg-blue-600 overflow-x-auto whitespace-nowrap">
      <div className="container mx-auto px-2 sm:px-4 flex space-x-1 sm:space-x-2 items-center">
        {allTabs.map(tab => (
          <div key={tab.id} className="flex items-center relative group">
            <button
              onClick={() => changeTab(tab.id)}
              className={`flex items-center px-3 py-3 text-sm sm:text-base font-medium border-b-2 transition-colors duration-150
                ${currentTab.id === tab.id 
                  ? 'border-white text-white' 
                  : 'border-transparent text-blue-200 hover:text-white hover:border-blue-300'
                }
              `}
              title={tab.name}
            >
              {getIconForTab(tab.id)}
              <span className="truncate max-w-[100px] sm:max-w-[150px]">{tab.name}</span>
              {tab.id === 'critical_members' && criticalMemberIds.length > 0 && (
                <span className="ml-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                  {criticalMemberIds.length}
                </span>
              )}
            </button>
            {isBacentaTab(tab.id) && currentTab.id === tab.id && (
              <div className="ml-1 flex items-center space-x-1">
                <button 
                  onClick={() => openBacentaForm(bacentas.find(b => b.id === tab.id) || null)} 
                  className="p-1 text-blue-200 hover:text-white rounded-full hover:bg-blue-700"
                  title={`Edit ${tab.name}`}
                  aria-label={`Edit ${tab.name}`}
                  >
                  <EditIcon className="w-4 h-4" />
                </button>
                <button 
                  onClick={() => deleteBacentaHandler(tab.id)} 
                  className="p-1 text-red-300 hover:text-white rounded-full hover:bg-red-500"
                  title={`Delete ${tab.name}`}
                  aria-label={`Delete ${tab.name}`}
                  >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        ))}
        <button
          onClick={() => openBacentaForm(null)}
          className="flex items-center px-3 py-3 text-sm sm:text-base font-medium text-blue-200 hover:text-white transition-colors duration-150"
          title="Add New Bacenta"
          aria-label="Add New Bacenta"
        >
          <PlusCircleIcon className="w-5 h-5 mr-1 sm:mr-2" />
          <span className="hidden sm:inline">Add Bacenta</span>
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
