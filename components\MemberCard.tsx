
import React from 'react';
import { Member } from '../types'; 
import { useAppData } from '../hooks/useAppData';
import AttendanceMarker from './AttendanceMarker';
import { formatDisplayDate, formatFullDate } from '../utils/dateUtils';
import { UserIcon, EditIcon, TrashIcon, WarningIcon, PhoneIcon, HomeIcon, CalendarIcon } from './icons';
import Button from './ui/Button';
import Badge from './ui/Badge';

interface MemberCardProps {
  member: Member;
  isCritical?: boolean;
}

const MemberCard: React.FC<MemberCardProps> = ({ member, isCritical }) => {
  const { displayedSundays, attendanceRecords, markAttendanceHandler, deleteMemberHandler, openMemberForm, bacentas, criticalMemberIds } = useAppData(); 

  const getAttendanceStatus = (date: string) => {
    const record = attendanceRecords.find(ar => ar.memberId === member.id && ar.date === date);
    return record?.status;
  };

  const memberBacenta = bacentas.find(b => b.id === member.bacentaId);

  const formatPhoneNumber = (phone: string) => {
    if(!phone) return 'N/A';
    const cleaned = ('' + phone).replace(/\D/g, '');
    // Basic US-like formatting, can be enhanced
    const matchNA = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (matchNA) return `(${matchNA[1]}) ${matchNA[2]}-${matchNA[3]}`;
    if (cleaned.length > 6) return `${cleaned.slice(0,3)}-${cleaned.slice(3,6)}-${cleaned.slice(6)}`;
    return phone;
  };

  const formatDateForTimestamp = (isoString: string): string => {
    if (!isoString || isoString === 'Invalid Date') return 'N/A';
    try {
      return new Date(isoString).toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
      });
    } catch (e) {
      return 'Invalid Date';
    }
  };


  return (
    <div className={`bg-white shadow-lg rounded-xl p-4 sm:p-5 mb-4 border-l-4 ${isCritical ? 'border-red-500' : 'border-blue-500'}`}>
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
        <div className="flex items-start mb-3 sm:mb-0">
          <div className={`p-2 rounded-full mr-3 ${isCritical ? 'bg-red-100' : 'bg-blue-100'}`}>
            <UserIcon className={`w-8 h-8 ${isCritical ? 'text-red-500' : 'text-blue-500'}`} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
              {member.firstName} {member.lastName}
              {isCritical && <span title="Critical: Consecutive Absences"><WarningIcon className="w-5 h-5 text-red-500 ml-2" /></span>}
            </h3>
            <p className="text-sm text-gray-500">{memberBacenta?.name || <span className="italic text-gray-400">Unassigned</span>}</p>
             {member.bornAgainStatus && <Badge color="green" size="sm" className="mt-1">Born Again</Badge>}
          </div>
        </div>
        <div className="flex space-x-2 mt-2 sm:mt-0 self-start sm:self-center">
          <Button size="sm" variant="ghost" onClick={() => openMemberForm(member)} leftIcon={<EditIcon className="w-4 h-4" />} aria-label="Edit Member">
            <span className="hidden sm:inline">Edit</span>
          </Button>
          <Button size="sm" variant="danger" onClick={() => deleteMemberHandler(member.id)} leftIcon={<TrashIcon className="w-4 h-4" />} aria-label="Delete Member">
             <span className="hidden sm:inline">Delete</span>
          </Button>
        </div>
      </div>

      {isCritical && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md text-sm text-red-700">
          <WarningIcon className="w-5 h-5 inline mr-1" />
          This member has {criticalMemberIds.includes(member.id) ? '2 or more' : ''} consecutive absences for the displayed month. Please follow up.
        </div>
      )}

      <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-700">
        <div className="flex items-center">
          <PhoneIcon className="w-4 h-4 mr-2 text-gray-400"/>
          <span>{formatPhoneNumber(member.phoneNumber) || <span className="text-gray-400 italic">No phone</span>}</span>
        </div>
         <div className="flex items-center">
          <HomeIcon className="w-4 h-4 mr-2 text-gray-400"/>
          <span>{member.buildingAddress || <span className="text-gray-400 italic">No address</span>}</span>
        </div>
        <div className="flex items-center">
          <CalendarIcon className="w-4 h-4 mr-2 text-gray-400"/>
          <span>Joined: {member.joinedDate ? formatDisplayDate(member.joinedDate) : <span className="text-gray-400 italic">N/A</span>}</span>
        </div>
      </div>


      <div className="mt-4 pt-3 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-600 mb-2">Attendance:</h4>
        {displayedSundays.length > 0 ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-x-4 gap-y-3">
            {displayedSundays.map(sundayDate => (
              <div key={sundayDate} className="flex flex-col items-center sm:flex-row sm:items-center justify-between p-2 bg-gray-50 rounded-md">
                <span className="text-xs text-gray-600 mb-1 sm:mb-0 sm:mr-2">{formatDisplayDate(sundayDate)}</span>
                <AttendanceMarker
                  memberId={member.id}
                  date={sundayDate}
                  currentStatus={getAttendanceStatus(sundayDate)}
                  onMarkAttendance={markAttendanceHandler}
                />
              </div>
            ))}
          </div>
        ) : (
          <p className="text-xs text-gray-500 italic">No Sundays scheduled for the selected month or data unavailable.</p>
        )}
      </div>
       <div className="mt-3 text-xs text-gray-400">
          Created: {formatDateForTimestamp(member.createdDate)} | Updated: {formatDateForTimestamp(member.lastUpdated)}
      </div>
    </div>
  );
};

export default MemberCard;
