
import React, { useEffect } from 'react';
import { useAppData } from './hooks/useAppData';
import Navbar from './components/Navbar';
import DashboardView from './components/DashboardView';
import MemberListView from './components/MemberListView';
import CriticalMembersView from './components/CriticalMembersView';
import { LoadingSpinnerIcon, RefreshIcon, PlusIcon as AddMemberIcon } from './components/icons'; // Renamed PlusIcon for clarity
import { TabKeys } from './types';
import MemberFormModal from './components/MemberFormModal';
import BacentaFormModal from './components/BacentaFormModal'; // Import BacentaFormModal

const App: React.FC = () => {
  const {
    currentTab,
    isLoading,
    error,
    fetchInitialData,
    isMemberFormOpen,
    editingMember,
    openMemberForm,
    closeMemberForm,
    refreshData,
    displayedSundays, 
    isBacentaFormOpen, 
    editingBacenta,    
    closeBacentaForm,  
    bacentas,
    members, // Added members to destructuring
  } = useAppData();

  useEffect(() => {
    fetchInitialData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // fetchInitialData itself will load current month's data

  const renderView = () => {
    // Initial loading state check
    if (isLoading && !displayedSundays.length && !bacentas.length && !members.length) { 
      return (
        <div className="flex flex-col items-center justify-center h-screen">
          <LoadingSpinnerIcon className="w-12 h-12 text-blue-500" />
          <p className="mt-2 text-gray-600">Loading Church Data...</p>
        </div>
      );
    }
    if (error) {
      return <div className="p-4 text-red-600 bg-red-100 rounded-md">Error: {error}</div>;
    }

    const isBacentaTab = bacentas.some(b => b.id === currentTab.id);

    if (isBacentaTab) {
      return <MemberListView bacentaFilter={currentTab.id} />;
    }

    switch (currentTab.id) {
      case TabKeys.DASHBOARD:
        return <DashboardView />;
      case TabKeys.CRITICAL_MEMBERS:
        return <CriticalMembersView />;
      case TabKeys.ALL_CONGREGATIONS: 
        return <MemberListView bacentaFilter={null} />;
      default: 
        return <MemberListView bacentaFilter={null} />; 
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      <header className="bg-blue-600 text-white shadow-md sticky top-0 z-50">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <h1 className="text-xl font-bold">Church Connect</h1>
          <button
            onClick={refreshData}
            className="p-2 rounded-full hover:bg-blue-700 transition-colors"
            aria-label="Refresh Data"
            title="Refresh Data"
          >
            <RefreshIcon className="w-6 h-6" />
          </button>
        </div>
        <Navbar />
      </header>

      <main className="flex-grow container mx-auto px-2 sm:px-4 py-4">
        {renderView()}
      </main>

      <footer className="bg-gray-800 text-white text-center p-4 text-sm">
        &copy; {new Date().getFullYear()} Church Connect Mobile. All data is stored locally.
      </footer>
      
      {isMemberFormOpen && (
        <MemberFormModal
          isOpen={isMemberFormOpen}
          onClose={closeMemberForm}
          member={editingMember}
        />
      )}

      {isBacentaFormOpen && ( 
        <BacentaFormModal
          isOpen={isBacentaFormOpen}
          onClose={closeBacentaForm}
          bacenta={editingBacenta}
        />
      )}

      <button
        onClick={() => openMemberForm(null)}
        className="fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-75 transition-transform duration-150 ease-in-out hover:scale-105"
        aria-label="Add New Member"
        title="Add New Member"
      >
        <AddMemberIcon className="h-7 w-7" viewBox="0 0 20 20" fill="currentColor" />
      </button>
    </div>
  );
};

export default App;
